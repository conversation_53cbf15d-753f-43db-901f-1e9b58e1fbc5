<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<text class="title">约球</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 运动类型标签 -->
			<scroll-view class="sports-tabs" scroll-x="true" show-scrollbar="false">
				<view v-for="(sport, index) in selectedSports" :key="index" class="sport-tab"
					:class="{ active: currentSport.id === sport.id }" @tap="selectSport(sport)">
					{{ sport.sportsName }}
				</view>
				<view class="add-sport-btn" @tap="sportModal" v-if="false">
					<text class="plus">+</text>
				</view>
			</scroll-view>

			<!-- 筛选条 -->
			<view class="filter-bar">
				<view class="tab-switch">
					<view class="tab" :class="{ active: !showUserEvents }" @tap="switchToAllEvents">全部约球</view>
					<view class="tab" :class="{ active: showUserEvents }" @tap="switchToUserEvents">我的约球</view>
				</view>
				<view class="filter-options">
					<picker :range="distanceOptions" @change="onDistanceChange" v-if="!showUserEvents">
						<view class="filter-item" style="margin-left: 0;">
							距离：{{ distanceOptions[formData.selectedDistanceIndex] }}
						</view>
					</picker>
					<picker :range="statusOptions" @change="onStatusChange" > 
						<view class="filter-item">
							状态：{{ statusOptions[formData.selectedStatusIndex] }}
						</view>
					</picker>
					<picker :range="timeOptions" @change="onTimeChange" v-if="false">
						<view class="filter-item">
							时间：{{ timeOptions[formData.selectedTimeIndex] }}
						</view>
					</picker>
				</view>
			</view>

			<!-- 约球活动列表 -->
			<scroll-view v-if="!showUserEvents" :scroll-top="scrollTop" class="events" scroll-y="true" lower-threshold="100"
				@scrolltolower="loadMore" refresher-enabled="true" :refresher-triggered="refreshTriggered"
				@refresherrefresh="onRefresh">
				<!-- 骨架屏 -->
				<view v-if="loading && events.length === 0" class="skeleton">
					<view v-for="i in 3" :key="i" class="skeleton-card">
						<view class="skeleton-image"></view>
						<view class="skeleton-info">
							<view class="skeleton-title"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-footer">
								<view class="skeleton-distance"></view>
								<view class="skeleton-btn"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 实际内容 -->
				<view v-else v-for="(event, index) in events" :key="index" class="event-card" @click="viewEventDetail(event.id)">
					<view class="event-header">
						<image :src="event.creator_avatar || '/static/default-avatar.png'" class="creator-avatar" />
						<view class="event-creator">
							<text class="creator-name">{{ event.creator_name }}</text>
							<text class="event-time">{{ formatDate(event.event_time) }}</text>
						</view>
						<view class="event-status" :class="{
							'status-active': event.status === 'active',
							'status-ended': event.status === 'ended',
							'status-canceled': event.status === 'canceled'
						}">
							{{ getStatusText(event.status) }}
						</view>
					</view>
					<view class="event-content">
						<view class="event-title">{{ event.title }}</view>
						<view class="event-info">
							<view class="info-item">
								<image src="/static/icons/sport.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ getSportName(event.sport_type) }}</text>
							</view>
							<view class="info-item">
								<image src="/static/icons/location.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ event.location_name }}</text>
							</view>
							<view class="info-item">
								<image src="/static/icons/time.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ formatTime(event.event_time) }}</text>
							</view>
							<view class="info-item">
								<image src="/static/icons/people.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ event.current_participants }}/{{ event.max_participants }}人</text>
							</view>
						</view>
						<view class="event-desc" v-if="event.description">{{ event.description }}</view>
						<view class="event-footer">
							<text class="distance">{{ getDistanceMeters(event.distance_meters) }}</text>
							<!-- <view class="join-btn" @tap.stop="joinEvent(event)"
								v-if="event.status === 'active' && !event.isJoined && event.current_participants < event.max_participants">
								加入
							</view> -->
							<view class="quit-btn"
								v-if="event.isJoined && event.status === 'active' && event.creator_id.toString() == $store.state.userinfo.id.toString()">
								我创建的
							</view>
							<view class="quit-btn" @tap.stop="quitEvent(event.id,event)"
								v-else-if="event.isJoined && event.status === 'active'">
								退出
							</view>
							<view class="ended-btn" v-else-if="event.status === 'ended'">
								已结束
							</view>
							<view class="full-btn" v-else-if="event.current_participants >= event.max_participants">
								已满
							</view>
							<view class="canceled-btn" v-else-if="event.status === 'canceled'">
								已取消
							</view>
						</view>
					</view>
				</view>

				<!-- 加载状态提示 -->
				<view class="loading-status">
					<text v-if="loading && events.length > 0">正在加载中...</text>
					<text v-else-if="!hasMore && events.length > 0">没有更多数据了</text>
					<view class="empty-guide" v-if="events.length === 0 && !loading">
						<image src="../../static/images/空状态.png" mode="widthFix" />
						<view class="empty-text">暂无约球活动</view>
						<button class="publish-btn" @click="createEvent">发起约球</button>
					</view>
				</view>
			</scroll-view>

			<!-- 我的约球活动列表 -->
			<scroll-view v-if="showUserEvents" class="events" scroll-y="true" lower-threshold="100"
				@scrolltolower="loadMoreUserEvents" refresher-enabled="true" :refresher-triggered="refreshTriggered"
				@refresherrefresh="onRefreshUserEvents">
				<!-- 骨架屏 -->
				<view v-if="userEventsLoading && userEvents.length === 0" class="skeleton">
					<view v-for="i in 3" :key="i" class="skeleton-card">
						<view class="skeleton-image"></view>
						<view class="skeleton-info">
							<view class="skeleton-title"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-footer">
								<view class="skeleton-distance"></view>
								<view class="skeleton-btn"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 实际内容 -->
				<view v-else v-for="(event, index) in userEvents" :key="index" class="event-card"
					@click="viewEventDetail(event.event_id)">
					<view class="event-header">
						<image :src="event.creator_avatar || '/static/default-avatar.png'" class="creator-avatar" />
						<view class="event-creator">
							<text class="creator-name">{{ event.creator_name }}</text>
							<text class="event-time">{{ formatDate(event.event_time) }}</text>
						</view>
						<view class="event-status" :class="{
							'status-active': event.status === 'active',
							'status-ended': event.status === 'ended',
							'status-canceled': event.status === 'canceled'
						}">
							{{ getStatusText(event.status) }}
						</view>
					</view>
					<view class="event-content">
						<view class="event-title">{{ event.title }}</view>
						<view class="event-info">
							<view class="info-item">
								<image src="/static/icons/sport.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ getSportName(event.sport_type) }}</text>
							</view>
							<view class="info-item">
								<image src="/static/icons/location.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ event.location_name }}</text>
							</view>
							<view class="info-item">
								<image src="/static/icons/time.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ formatTime(event.event_time) }}</text>
							</view>
							<view class="info-item">
								<image src="/static/icons/people.svg" class="info-icon" mode="aspectFit"></image>
								<text class="info-text">{{ event.current_participants }}/{{ event.max_participants }}人</text>
							</view>
						</view>
						<view class="event-desc" v-if="event.description">{{ event.description }}</view>
						<view class="event-footer">
							<text class="distance">{{$getDistance(event.latitude,event.longitude,formData.latitude,formData.longitude)}}</text>
						
							<view class="quit-btn"
								v-if="event.status === 'active' && event.creator_id.toString() == $store.state.userinfo.id.toString()">
								我创建的	
							</view>
							<view class="quit-btn" @tap.stop="quitEvent(event.event_id,event)"
								v-else-if="event.status === 'active'&& event.creator_id.toString() != $store.state.userinfo.id.toString()">
								退出
							</view>
							<view class="ended-btn" v-else-if="event.status === 'ended'">
								已结束
							</view>
							<view class="canceled-btn" v-else-if="event.status === 'canceled'">
								已取消
							</view>
							<view class="creator-btn" v-else-if="event.isCreator">
								我创建的
							</view>
						</view>
					</view>
				</view>

				<!-- 加载状态提示 -->
				<view class="loading-status">
					<text v-if="userEventsLoading && userEvents.length > 0">正在加载中...</text>
					<text v-else-if="!userEventsHasMore && userEvents.length > 0">没有更多数据了</text>
					<view class="empty-guide" v-if="userEvents.length === 0 && !userEventsLoading">
						<image src="../../static/images/空状态.png" mode="widthFix" />
						<view class="empty-text">暂无参与的约球活动</view>
						<button class="publish-btn" @click="createEvent">发起约球</button>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 运动选择弹窗 -->
		<view class="sport-modal" v-if="showSportModal" @tap="closeSportModal">
			<view class="sport-options" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">选择感兴趣的运动</text>
					<text class="modal-subtitle">最多可选择5项</text>
				</view>
				<view class="sport-grid">
					<view v-for="sport in sports" :key="sport.id" class="sport-item"
						:class="{ selected: selectedSports.some(item => item.id == sport.id) }" @tap="toggleSport(sport)">
						{{ sport.sportsName }}
					</view>
				</view>
				<button class="confirm-btn" @tap="confirmSports" :disabled="selectedSports.length === 0">
					确定
				</button>
			</view>
		</view>

		<!-- 创建约球按钮 -->
		<view class="create-event" @click="createEvent">
			<text class="create-icon">发起约球</text>
		</view>
	</view>
</template>

<script>
import {
	getSportsEvents,
	joinSportsEvent,
	quitSportsEvent,
	getUserEvents,
	subscribe
} from "../../Api/index.js";

import {
	debounce
} from '@/utools/index.js'
import Mixins from "../../minix/index.js"

export default {
	mixins: [Mixins],
	data() {
		return {
			scrollTop: 0,
			formData: {	
				latitude: "",
				longitude: "",
				selectedDistanceIndex: 5,
				selectedTimeIndex: 0,
				selectedStatusIndex: 1,
				page: 1,
				pageSize: 10,
				sportType: '',
				distance: 20,
				timeRange: 'all',
				status: 'active'
			},
			// 用户约球列表
			userEventsFormData: {
				page: 1,
				pageSize: 10,
				sportType: '',
				selectedStatusIndex: 0,
				status: 'all'
			},
			// 用户约球活动列表
			userEvents: [],
			// 用户约球活动加载状态
			userEventsLoading: false,
			// 用户约球活动是否有更多数据
			userEventsHasMore: true,
			// 当前是否显示用户约球活动列表
			showUserEvents: false,
			refreshTriggered: false, // 下拉刷新状态
			loading: true,
			hasMore: true,
			// 运动类型列表
			sports: [],
			// 用户选择的运动类型
			selectedSports: [],
			// 当前选中的运动类型
			currentSport: {},
			// 是否显示运动选择弹窗
			showSportModal: false,
			// 约球活动列表
			events: [],
			distanceOptions: [ '1Km', '2km', '3Km', '5Km', '10Km', '20Km'],
			statusOptions: ['全部', '进行中', '已结束', '已取消'],
			timeOptions: ['不限', '今天', '明天', '本周', '下周'],
			isReady: false,
		}
	},

	mounted() {
		this.isReady = false


		this.getCurrentLocation()
	},
	watch: {
		"$store.state.sportsWord": {
			handler(newval, oldval) {
				this.sports = [...newval]
			},
			deep: true,
		},
		currentSport(newval) {
			this.formData.sportType = newval.id
			this.userEventsFormData.sportType = newval.id
			this.events = []
			this.userEvents = []
			this.formData.page = 1
			this.userEventsFormData.page = 1

			// 设置加载状态，但根据当前页面类型设置不同的loading状态
			if (this.showUserEvents) {
				this.userEventsLoading = true
			} else {
				this.loading = true
			}

			this.$nextTick(() => {
				this.scrollTop = 0
			})

			// 如果在我的约球页面，需要刷新我的约球数据
			if (this.showUserEvents) {
				this.getUserEventsData(this.userEventsFormData)
			}
		},
		formData: {
			handler(newval) {
				if (this.isReady && !this.showUserEvents) {
					this.getEventsData(newval)
				}
			},
			deep: true,
		},
		userEventsFormData: {
			handler(newval) {
				if (this.isReady && this.showUserEvents) {
					this.getUserEventsData(newval)
				}
			},
			deep: true,
		},
		showUserEvents(newval) {
			if (newval) {
				// 切换到我的约球页面
				this.getUserEventsData(this.userEventsFormData)
			} else {
				// 切换到全部约球页面
				this.getEventsData(this.formData)
			}
		}
	},
	onLoad() {
		this.isReady = false

		// 监听从工作台发来的切换事件
		uni.$on('switchToMyEvents', () => {
			this.switchToUserEvents()
		})
	},

	onUnload() {
		// 移除事件监听
		uni.$off('switchToMyEvents')
	},

	onShow() {
		// 检查是否有从详情页返回保存的当前运动类型
		const savedSportType = uni.getStorageSync('currentSportType')
		
		// 检查是否有已选择的运动类型
		let data = uni.getStorageSync('selectedSports')
		if (data) {
			data = JSON.parse(data)
			this.selectedSports = data
			
			// 如果有保存的运动类型ID，则使用该类型
			if (savedSportType) {
				const targetSport = this.selectedSports.find(sport => sport.id.toString() === savedSportType.toString())
				if (targetSport) {
					this.currentSport = targetSport
					// 使用后清除，避免影响后续其他页面操作
					uni.removeStorageSync('currentSportType')
				} else {
					this.currentSport = this.selectedSports[0]
				}
			} else {
				this.currentSport = this.selectedSports[0]
			}
		} else {
			// 默认选择第一个运动类型
			if (this.$store.state.sportsWord && this.$store.state.sportsWord.length > 0) {
				this.selectedSports = [this.$store.state.sportsWord[0]]
				this.currentSport = this.$store.state.sportsWord[0]
			}
		}

		// 设置表单中的运动类型
		if (this.currentSport && this.currentSport.id) {
			this.formData.sportType = this.currentSport.id
			this.userEventsFormData.sportType = this.currentSport.id
		}
		// 每次显示页面时刷新数据
		if (this.isReady) {
			this.refreshEvents()
		}
	},
	methods: {
		// 切换到全部约球
		switchToAllEvents() {
			if (this.showUserEvents) {
				this.showUserEvents = false
				// 同步当前状态筛选
				this.formData.selectedStatusIndex = this.userEventsFormData.selectedStatusIndex
				this.formData.status = this.userEventsFormData.status
			}
		},

		// 切换到我的约球
		switchToUserEvents() {
			// 检查用户是否已登录
			const token = uni.getStorageSync('ydToken')
			if (!token || !this.$store.state.userinfo.nickname) {
				uni.showModal({
					title: '提示',
					content: '请先登录后再查看我的约球',
					confirmText: '去登录',
					success: (res) => {
						if (res.confirm) {
							uni.switchTab({
								url: '/pages/profile/profile'
							})
						}
					}
				})
				return
			}

			if (!this.showUserEvents) {
				this.showUserEvents = true
				// 同步当前状态筛选
				this.userEventsFormData.selectedStatusIndex = this.formData.selectedStatusIndex
				this.userEventsFormData.status = this.formData.status
			}
		},

		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.formData.latitude = res.latitude
					this.formData.longitude = res.longitude
					this.isReady = true
					if (this.showUserEvents) {
						this.getUserEventsData(this.userEventsFormData)
					} else {
						this.getEventsData(this.formData)
					}
				},
				fail: () => {
					uni.showToast({
						title: '获取位置失败，请检查定位权限',
						icon: 'none'
					})
					this.isReady = true
				}
			})
		},
		// 获取约球活动列表
		getEventsData: debounce(function (data) {
			if (!data.latitude) {
				return false
			}

			this.loading = true
			data.userId = this.$store.state.userinfo.id
			getSportsEvents(data).then(res => {
				if (res.data.code === 200) {
					const newEvents = res.data.data.events || []

					// 如果是第一页，直接替换
					if (data.page === 1) {
						this.events = newEvents
					} else {
						// 否则追加
						this.events = [...this.events, ...newEvents]
					}

					// 判断是否还有更多数据
					this.hasMore = newEvents.length >= data.pageSize
					console.log(this.hasMore);

				} else {
					uni.showToast({
						title: res.data.message || '获取数据失败',
						icon: 'none'
					})
				}
				this.loading = false
				this.refreshTriggered = false
			}).catch((err) => {
				console.error('获取约球活动列表失败', err)
				this.loading = false
				this.refreshTriggered = false
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				})
			})
		}, 500),
		// 下拉刷新
		onRefresh() {
			this.formData.page = 1
			this.refreshTriggered = true
			this.getEventsData({ ...this.formData })
		},
		// 刷新约球活动
		refreshEvents() {
			if (this.showUserEvents) {
				this.userEventsFormData.page = 1
				this.getUserEventsData({ ...this.userEventsFormData })
			} else {
				this.formData.page = 1
				this.getEventsData({ ...this.formData })
			}
		},

		// 获取运动名称映射表
		getSportsMapping() {
			// 将选中的运动类型转换为id->name的映射
			const mapping = {}
			this.selectedSports.forEach(sport => {
				mapping[sport.id] = sport.sportsName
			})
			return mapping
		},
		// 加载更多
		loadMore() {
			if (this.loading || !this.hasMore) return
			this.formData.page++
			this.getEventsData({ ...this.formData })
		},
		// 下拉刷新用户约球活动
		onRefreshUserEvents() {
			this.userEventsFormData.page = 1
			this.refreshTriggered = true
			this.getUserEventsData({ ...this.userEventsFormData })
		},
		// 加载更多用户约球活动
		loadMoreUserEvents() {
			if (this.userEventsLoading || !this.userEventsHasMore) return
			this.userEventsFormData.page++
			this.getUserEventsData({ ...this.userEventsFormData })
		},
		// 获取用户参与的约球活动
		getUserEventsData: debounce(function (data) {
			// 检查用户是否已登录
			const token = uni.getStorageSync('ydToken')
			if (!token || !this.$store.state.userinfo.nickname) {
				this.refreshTriggered = false
				return
			}

			this.userEventsLoading = true

			// 调用获取用户约球活动的接口
			data.userId = this.$store.state.userinfo.id
			getUserEvents(data).then(res => {
				if (res.data.code === 200) {
					const newEvents = res.data.data.events || []

					// 如果是第一页，直接替换
					if (data.page === 1) {
						this.userEvents = newEvents
					} else {
						// 否则追加
						this.userEvents = [...this.userEvents, ...newEvents]
					}

					// 判断是否还有更多数据
					this.userEventsHasMore = newEvents.length >= data.pageSize
				} else {
					uni.showToast({
						title: res.data.message || '获取数据失败',
						icon: 'none'
					})
				}
				this.userEventsLoading = false
				this.refreshTriggered = false
			}).catch((err) => {
				console.error('获取用户约球活动失败', err)
				this.userEventsLoading = false
				this.refreshTriggered = false
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				})
			})
		}, 500),
		// 选择运动类型
		selectSport(sport) {
			this.scrollTop = 100
			this.currentSport = sport
		},
		// 显示运动选择弹窗
		sportModal() {
			this.showSportModal = true
		},
		// 关闭运动选择弹窗
		closeSportModal() {
			this.showSportModal = false
		},
		// 切换运动类型选择
		toggleSport(sport) {
			const index = this.selectedSports.findIndex(item => item.id === sport.id)
			if (index > -1) {
				// 已选中，取消选择
				this.selectedSports.splice(index, 1)
			} else {
				// 未选中，添加选择（最多5个）
				if (this.selectedSports.length < 5) {
					this.selectedSports.push(sport)
				} else {
					uni.showToast({
						title: '最多只能选择5个运动类型',
						icon: 'none'
					})
				}
			}
		},
		// 确认运动类型选择
		confirmSports() {
			if (this.selectedSports.length === 0) {
				uni.showToast({
					title: '请至少选择一个运动类型',
					icon: 'none'
				})
				return
			}

			// 保存选择
			uni.setStorageSync('selectedSports', JSON.stringify(this.selectedSports))

			// 如果当前选中的运动不在选择列表中，则默认选择第一个
			if (!this.selectedSports.some(item => item.id === this.currentSport.id)) {
				this.currentSport = this.selectedSports[0]
			}

			this.closeSportModal()
			this.refreshEvents()
		},
		// 距离选择变更
		onDistanceChange(e) {
			this.formData.selectedDistanceIndex = e.detail.value

		
				// 根据选项设置具体距离值
				const distances = [ 1, 2, 3, 5, 10, 20]
				this.formData.distance = distances[e.detail.value]
			
		},
		// 状态选择变更
		onStatusChange(e) {
			this.formData.selectedStatusIndex = e.detail.value
			
			// 根据选项设置具体状态值
			const statuses = ['all', 'active', 'ended', 'canceled']
			if (this.showUserEvents) {
				this.userEventsFormData.selectedStatusIndex = e.detail.value
				this.userEventsFormData.status = statuses[e.detail.value]
				this.userEventsFormData.page = 1
				this.userEvents = []
				this.userEventsLoading = true
				this.getUserEventsData({...this.userEventsFormData})
			} else {
				this.formData.status = statuses[e.detail.value]
				this.formData.page = 1
				this.events = []
				this.loading = true
				this.getEventsData({...this.formData})
			}
		},
		// 时间选择变更
		onTimeChange(e) {
			this.formData.selectedTimeIndex = e.detail.value

			// 根据选择设置时间范围
			const timeRanges = ['all', 'today', 'tomorrow', 'thisWeek', 'nextWeek']
			this.formData.timeRange = timeRanges[e.detail.value]
		},
		// 格式化日期
		formatDate(timestamp) {
			const date = new Date(timestamp)
			return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
		},
		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp)
			return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
		},
		// 获取距离文本
		getDistanceMeters(m) {
			if (!m) return '未知距离'
			if (m < 1000) {
				return Math.round(m) + '米'
			} else {
				return (m / 1000).toFixed(1) + '公里'
			}
		},
		// 获取运动名称
		getSportName(sportId) {
			const sport = this.selectedSports.find(item => item.id === sportId)
			return sport ? sport.sportsName : '未知运动'
		},
		// 获取状态文本
		getStatusText(status) {
			switch (status) {
				case 'active': return '进行中'
				case 'ended': return '已结束'
				case 'canceled': return '已取消'
				default: return '未知'
			}
		},
		// 查看约球活动详情
		viewEventDetail(id) {
			// 传递当前的运动类型ID，用于返回时保持同一个运动类型
			const sportTypeId = this.currentSport.id
			uni.navigateTo({
				url: `/subpages/sports-event-detail/sports-event-detail?id=${id}&sportType=${sportTypeId}`
			})
		},
		// 加入约球活动
		joinEvent(event) {
			// 检查用户是否已登录
			const token = uni.getStorageSync('ydToken');
			if (!token || !this.$store.state.userinfo.nickname) {
				// 未登录，提示用户并跳转到我的页面
				uni.showModal({
					title: '提示',
					content: '请先登录后再加入约球',
					confirmText: '去登录',
					success: (res) => {
						if (res.confirm) {
							uni.switchTab({
								url: '/pages/profile/profile'
							})
						}
					}
				})
				return;
			}

			uni.requestSubscribeMessage({
				tmplIds: ['o-8tpZjbcOPkrrw54S7SXX9Erk3qlQ8997NI6Yj2BAY'],
				success: (res) => {
					uni.showLoading({
						title: '加入中...'
					})
					let sub_num = -1
					let str = res['o-8tpZjbcOPkrrw54S7SXX9Erk3qlQ8997NI6Yj2BAY']
					if (str == 'accept') {
						sub_num=1
					} else {
						sub_num=0
					}
					// 直接调用加入接口，默认不分享联系方式
					joinSportsEvent({
						eventId: event.id,
						sub_num: sub_num,
						show_contact: 0,  // 默认不分享联系方式
						phone: '',
						wechat: ''
					}).then(re => {

						uni.hideLoading()

						if (re.data.code === 200) {
							uni.showToast({
								title: '加入成功',
								icon: 'success'
							})
							// 刷新活动列表
							this.refreshEvents()
						} else {
							uni.showToast({
								title: re.data.message || '加入失败',
								icon: 'none'
							})
						}
					}).catch(() => {
						uni.hideLoading()
						uni.showToast({
							title: '网络错误，请稍后重试',
							icon: 'none'
						})
					})
				},
				fail: (err) => {
					console.error('订阅消息失败', err)
					uni.$showToast({
						title: '订阅消息失败，请稍后重试',
						icon: 'none'
					})
				}
			})




		},

		// 退出约球活动
		quitEvent(id,event) {
			// 检查活动开始前30分钟是否允许退出
			const now = new Date().getTime();
			const eventTime = new Date(event.event_time).getTime();
			const thirtyMinutes = 30 * 60 * 1000; // 30分钟的毫秒数
			
			if (eventTime - now < thirtyMinutes) {
				uni.showModal({
					title: '提示',
					content: '活动即将开始，已不允许退出',
					showCancel: false
				});
				return;
			}
			uni.showModal({
				title: '提示',
				content: '确定要退出该约球活动吗？',
				success: (res) => {
					if (res.confirm) {

						uni.showLoading({
							title: '处理中...'
						})

						quitSportsEvent({
							eventId: id
						}).then(res => {
							uni.hideLoading()

							if (res.data.code === 200) {
								uni.showToast({
									title: '已退出活动',
									icon: 'success'
								})
								// 刷新活动列表
								this.refreshEvents()
							} else {
								uni.showToast({
									title: res.data.message || '退出失败',
									icon: 'none'
								})
							}
						}).catch(() => {
							uni.hideLoading()
							uni.showToast({
								title: '网络错误，请稍后重试',
								icon: 'none'
							})
						})
					}
				}
			})
		},
		// 创建约球活动
		createEvent() {
			// 检查用户是否已登录
			const token = uni.getStorageSync('ydToken');
			if (!token || !this.$store.state.userinfo.nickname) {
				// 未登录，提示用户并跳转到我的页面
				uni.showModal({
					title: '提示',
					content: '请先登录后再发起约球',
					confirmText: '去登录',
					success: (res) => {
						if (res.confirm) {
							uni.switchTab({
								url: '/pages/profile/profile'
							})
						}
					}
				})
				return;
			}

			// 已登录，跳转到创建约球页面
			uni.navigateTo({
				url: '/subpages/create-sports-event/create-sports-event'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	text-align: center;
	position: relative;

	.title {
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	flex: 1;
	// margin-top: calc(var(--status-bar-height) + 44px);
	// padding-bottom: 20rpx;
}

.sports-tabs {
	display: flex;
	white-space: nowrap;
	background-color: #ffffff;
	padding: 10px 15px;
	margin-bottom: 10px;
}

.sport-tab {
	display: inline-block;
	padding: 6px 16px;
	margin-right: 10px;
	border-radius: 20px;
	font-size: 14px;
	background-color: #f0f0f0;
	color: #666666;
}

.sport-tab.active {
	background-color: #4CAF50;
	color: #ffffff;
}

.add-sport-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border-radius: 16px;
	background-color: #f0f0f0;
	color: #666666;
}

.plus {
	font-size: 20px;
	line-height: 20px;
}

.filter-bar {
	display: flex;
	padding: 10px 10px;
	background-color: #ffffff;
	margin-bottom: 10px;
	justify-content: space-between;
	align-items: center;
}

.tab-switch {
	display: flex;
	border-radius: 20px;
	background-color: #f0f0f0;
	overflow: hidden;
}

.tab {
	padding: 6px 16px;
	font-size: 14px;
	color: #666666;
}

.tab.active {
	background-color: #4CAF50;
	color: #ffffff;
}

.filter-options {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	justify-content: flex-end;
}

.filter-item {
	margin-left: 10rpx;
	font-size: 14px;
	color: #666666;
	display: flex;
	align-items: center;
	background-color: #f0f0f0;
	padding: 4rpx 10rpx;
	border-radius: 20rpx;
}

.events {
	height: calc(100vh - var(--status-bar-height) - 44px - 120px);
}

.event-card {
	margin: 10px 15px;
	border-radius: 10px;
	background-color: #ffffff;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.event-header {
	display: flex;
	align-items: center;
	padding: 10px 15px;
	border-bottom: 1px solid #f0f0f0;
}

.creator-avatar {
	width: 36px;
	height: 36px;
	border-radius: 18px;
	margin-right: 10px;
}

.event-creator {
	flex: 1;
}

.creator-name {
	font-size: 14px;
	font-weight: bold;
	color: #333333;
	display: block;
}

.event-time {
	font-size: 12px;
	color: #999999;
}

.event-status {
	font-size: 12px;
	padding: 2px 6px;
	border-radius: 4px;
}

.status-active {
	background-color: #e8f5e9;
	color: #4CAF50;
}

.status-ended {
	background-color: #f5f5f5;
	color: #999999;
}

.status-canceled {
	background-color: #ffebee;
	color: #f44336;
}

.event-content {
	padding: 15px;
}

.event-title {
	font-size: 16px;
	font-weight: bold;
	color: #333333;
	margin-bottom: 10px;
}

.event-info {
	margin-bottom: 10px;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 5px;
}

.info-icon {
	margin-right: 5px;
	width: 16px;
	height: 16px;
	flex-shrink: 0;
	vertical-align: middle;
}

.info-text {
	font-size: 14px;
	color: #666666;
}

.event-desc {
	font-size: 14px;
	color: #666666;
	margin-bottom: 10px;
	line-height: 1.4;
}

.event-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 10px;
}

.distance {
	font-size: 14px;
	color: #999999;
}

.join-btn,
.quit-btn,
.full-btn,
.ended-btn,
.canceled-btn,
.creator-btn {
	padding: 6px 16px;
	border-radius: 20px;
	font-size: 14px;
	margin: 0;
	line-height: 1.2;
}

.join-btn {
	background-color: #4CAF50;
	color: #ffffff;
}

.quit-btn {
	background-color: #FF9800;
	color: #ffffff;
}

.creator-btn {
	background-color: #2196F3;
	color: #ffffff;
}

.full-btn,
.ended-btn,
.canceled-btn {
	background-color: #f0f0f0;
	color: #999999;
}

.loading-status {
	padding: 15px;
	text-align: center;
	color: #999999;
	font-size: 14px;
}

.empty-guide {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30px 0;
}

.empty-guide image {
	width: 150px;
	margin-bottom: 15px;
}

.empty-text {
	font-size: 16px;
	color: #999999;
	margin-bottom: 20px;
}

.publish-btn {
	background-color: #4CAF50;
	color: #ffffff;
	padding: 8px 20px;
	border-radius: 20px;
	font-size: 14px;
}

.sport-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.sport-options {
	width: 80%;
	max-width: 600px;
	background-color: #ffffff;
	border-radius: 10px;
	padding: 20px;
}

.modal-header {
	text-align: center;
	margin-bottom: 20px;
}

.modal-title {
	font-size: 18px;
	font-weight: bold;
	color: #333333;
	display: block;
}

.modal-subtitle {
	font-size: 14px;
	color: #999999;
}

.sport-grid {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20px;
}

.sport-item {
	width: 30%;
	margin: 5px 1.66%;
	padding: 8px 0;
	text-align: center;
	border-radius: 20px;
	font-size: 14px;
	background-color: #f0f0f0;
	color: #666666;
}

.sport-item.selected {
	background-color: #4CAF50;
	color: #ffffff;
}

.confirm-btn {
	background-color: #4CAF50;
	color: #ffffff;
	padding: 8px 0;
	border-radius: 20px;
	font-size: 16px;
	width: 80%;
	margin: 0 auto;
}

.confirm-btn[disabled] {
	background-color: #cccccc;
	color: #ffffff;
}

.create-event {
	position: fixed;
	left: 50%;
	transform: translateX(-50%);
	bottom: 100rpx;
	border-radius: 20rpx;
	background-color: #4CAF50;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
	z-index: 99;
	padding: 15rpx 25rpx;
}

.create-icon {
	font-size: 35rpx;
}

/* 骨架屏样式 */
.skeleton {
	padding: 10px 15px;
}

.skeleton-card {
	margin-bottom: 15px;
	background-color: #ffffff;
	border-radius: 10px;
	overflow: hidden;
}

.skeleton-image {
	height: 120px;
	background-color: #f0f0f0;
}

.skeleton-info {
	padding: 15px;
}

.skeleton-title {
	height: 20px;
	width: 60%;
	background-color: #f0f0f0;
	margin-bottom: 10px;
	border-radius: 4px;
}

.skeleton-line {
	height: 16px;
	width: 100%;
	background-color: #f0f0f0;
	margin-bottom: 10px;
	border-radius: 4px;
}

.skeleton-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.skeleton-distance {
	height: 16px;
	width: 30%;
	background-color: #f0f0f0;
	border-radius: 4px;
}

.skeleton-btn {
	height: 32px;
	width: 80px;
	background-color: #f0f0f0;
	border-radius: 16px;
}
</style>