<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">{{ groupName || '群聊' }}</text>
			</view>
		</view>

		<!-- 网络状态提示 -->
		<view class="network-tip" v-if="showNetworkTip">
			<text class="network-text" v-if="isReconnecting">正在重连...</text>
			<text class="network-text" v-else>网络连接已断开</text>
		</view>

		<!-- 聊天内容区域 -->
		<view class="chat-content">
			<scroll-view
				class="message-list"
				scroll-y
				:scroll-top="scrollTop"
				:scroll-into-view="scrollIntoView"
				:enable-back-to-top="false"
				:scroll-with-animation="true"
				:enhanced="true"
				:bounces="false"
				@scrolltoupper="loadMoreMessages">
				
				<!-- 加载更多提示 -->
				<view class="load-more" v-if="hasMoreMessages">
					<text class="load-more-text">上拉加载更多消息</text>
				</view>
				
				<!-- 消息列表 -->
				<view
					v-for="(message, index) in messageList"
					:key="message.ID"
					:id="`msg-${index}`"
					class="message-item"
					:class="{
						'message-self': message.from === currentUserID && !isSystemMessage(message),
						'message-system': isSystemMessage(message)
					}">

					<!-- 时间分割线 -->
					<view class="time-divider" v-if="shouldShowTime(message, index)">
						<text class="time-text">{{ formatMessageTime(message.time) }}</text>
					</view>

					<!-- 系统消息 -->
					<view class="system-message-wrapper" v-if="isSystemMessage(message)">
						<view class="message-bubble system-message">
							<!-- 群提示消息 -->
							<view class="message-content" v-if="message.type === 'TIMGroupTipElem'">
								<text class="message-text system-text">{{ formatGroupTipMessage(message) }}</text>
							</view>

							<!-- 群系统通知消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMGroupSystemNoticeElem'">
								<text class="message-text system-text">{{ formatGroupSystemNotice(message) }}</text>
							</view>
						</view>
					</view>

					<!-- 普通消息 -->
					<view class="message-wrapper" v-else>
						<!-- 头像 -->
						<image
							class="avatar"
							:src="message.avatar || '/static/default-avatar.png'"
							mode="aspectFill" />

						<!-- 消息气泡 -->
						<view class="message-bubble">
							<!-- 用户昵称 -->
							<text class="nickname" v-if="message.from !== currentUserID">{{ message.nick || message.from }}</text>

							<!-- 文本消息 -->
							<view class="message-content" v-if="message.type === 'TIMTextElem'">
								<text class="message-text">{{ message.payload.text }}</text>
							</view>

							<!-- 图片消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMImageElem'">
								<image
									class="message-image"
									:src="getImageUrl(message.payload)"
									mode="aspectFit"
									@tap="previewImage(message.payload)" />
							</view>

							<!-- 音频消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMAudioElem'">
								<view class="audio-message" @tap="playAudio(message.payload)">
									<text class="audio-icon">🎵</text>
									<text class="message-text">语音 {{ message.payload.second }}''</text>
								</view>
							</view>

							<!-- 视频消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMVideoElem'">
								<view class="video-message" @tap="playVideo(message.payload)">
									<image
										class="video-thumb"
										:src="message.payload.snapshotUrl || message.payload.thumbUrl"
										mode="aspectFit" />
									<text class="video-play-icon">▶</text>
								</view>
							</view>

							<!-- 文件消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMFileElem'">
								<view class="file-message" @tap="downloadFile(message.payload)">
									<text class="file-icon">📄</text>
									<view class="file-info">
										<text class="file-name">{{ message.payload.fileName }}</text>
										<text class="file-size">{{ formatFileSize(message.payload.fileSize) }}</text>
									</view>
								</view>
							</view>

							<!-- 位置消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMLocationElem'">
								<view class="location-message">
									<text class="location-icon">📍</text>
									<text class="message-text">{{ message.payload.description }}</text>
								</view>
							</view>

							<!-- 表情消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMFaceElem'">
								<text class="face-message">{{ message.payload.data }}</text>
							</view>

							<!-- 自定义消息 -->
							<view class="message-content" v-else-if="message.type === 'TIMCustomElem'">
								<text class="message-text">[自定义消息]</text>
							</view>

							<!-- 其他未知类型消息 -->
							<view class="message-content" v-else>
								<text class="message-text">[暂不支持此类型消息]</text>
							</view>

							<!-- 消息状态 -->
							<view class="message-status" v-if="message.from === currentUserID">
								<text class="status-text" v-if="message.status === 'sending'">发送中</text>
								<text class="status-text" v-else-if="message.status === 'success'">已发送</text>
								<view class="status-fail" v-else-if="message.status === 'fail'">
									<text class="status-text error">发送失败</text>
									<text class="retry-btn" @tap="retryMessage(message)">重试</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 占位元素，用于滚动到底部 -->
				<view id="bottom-anchor" class="bottom-anchor"></view>
			</scroll-view>
		</view>

		<!-- 输入区域 -->
		<view class="input-area">
			<view class="input-wrapper">
				<input 
					class="message-input" 
					type="text" 
					v-model="inputMessage"
					placeholder="输入消息..."
					@confirm="sendMessage"
					:focus="inputFocus"
					@blur="onInputBlur"
					@focus="onInputFocus" />
				<button 
					class="send-btn" 
					:disabled="!inputMessage.trim()"
					@tap="sendMessage">
					发送
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import imManager from "../../utils/IMManager.js"
import {  mapGetters } from 'vuex';
export default {
	data() {
		return {
			groupId: '',
			groupName: '',
			currentUserID: '',
			messageList: [],
			inputMessage: '',
			inputFocus: false,
			scrollTop: 0,
			scrollIntoView: '',
			hasMoreMessages: true,
			isLoadingMessages: false,
			lastMessageTime: 0,
			isConnected: true,
			showNetworkTip: false,
			isReconnecting: false,
			scrollTimer: null, // 滚动防抖定时器
			isScrolling: false // 滚动状态标志
		}
	},
	
	onLoad(options) {
		if (options.groupId) {
			this.groupId = options.groupId
		}
		if (options.groupName) {
			this.groupName = decodeURIComponent(options.groupName)
		}
		
		// 获取当前用户ID
		const loginStatus = imManager.getLoginStatus()
		this.currentUserID = loginStatus.currentUserID
		
		// 检查IM登录状态和SDK状态
		if (!loginStatus.isLoggedIn || !imManager.isSDKReady) {
			uni.showToast({
				title: 'IM未登录或SDK未准备就绪',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
			return
		}
		
		// 加载消息历史
		this.loadMessageHistory()
		imManager.onNewMessage = this.onNewMessage
		// 注册消息接收回调
		// imManager.onMessageReceived(this.onNewMessage)

		// 监听网络状态变化
		uni.onNetworkStatusChange(this.onNetworkStatusChange)

		// 注册IM网络状态回调
		imManager.onNetStateChange = this.onIMNetStateChange
	},
	
	onReady() {
		// 页面渲染完成后，再次确保滚动到底部
		setTimeout(() => {
			console.log('页面渲染完成，执行滚动到底部')
			this.scrollToBottom()
		}, 500)
	},

	onShow() {
		// 页面显示时标记消息为已读
		this.markConversationAsRead()
	},

	onUnload() {
		// 移除消息接收回调
		imManager.offMessageReceived(this.onNewMessage)

		// 移除网络状态监听
		uni.offNetworkStatusChange(this.onNetworkStatusChange)

		// 清空参与者信息
		this.$store.commit('CLEAR_PARTICIPANTS')
	},
	computed: {
	  ...mapGetters(['getUserInfo', 'getParticipantAvatar'])
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 加载消息历史
		async loadMessageHistory() {
			if (this.isLoadingMessages) return

			this.isLoadingMessages = true
			try {
				console.log('开始加载消息历史...')
				const result = await imManager.getGroupMessageList(this.groupId, 15)
				if (result.success) {
					const messages = result.data.messageList || []
					console.log('messages',messages);

					console.log('加载到消息数量:', messages.length)
					this.messageList = this.processMessages(messages)
					this.hasMoreMessages = !result.data.isCompleted

					// 标记会话为已读
					this.markConversationAsRead()

					// 确保DOM更新后再滚动到底部
					this.$nextTick(() => {
						setTimeout(() => {
							console.log('消息历史加载完成，滚动到底部')
							this.scrollToBottom()
						}, 200) // 增加延迟确保渲染完成
					})
				} else {
					console.error('加载消息历史失败:', result.error)
				}
			} catch (error) {
				console.error('加载消息历史异常:', error)
			}
			this.isLoadingMessages = false
		},
		
		// 加载更多消息
		async loadMoreMessages() {
			if (this.isLoadingMessages || !this.hasMoreMessages) return

			this.isLoadingMessages = true
			try {
				const result = await imManager.getGroupMessageList(this.groupId, 15)
				
				if (result.success) {
					const messages = result.data.messageList || []
					const newMessages = this.processMessages(messages)
					
					// 过滤掉已存在的消息
					const filteredMessages = newMessages.filter(newMsg => 
						!this.messageList.some(existMsg => existMsg.ID === newMsg.ID)
					)
					
					// 将新消息添加到列表开头
					this.messageList = [...filteredMessages, ...this.messageList]
					this.hasMoreMessages = !result.data.isCompleted
				}
			} catch (error) {
				console.error('加载更多消息异常:', error)
			}
			this.isLoadingMessages = false
		},
		
		// 处理消息数据
		processMessages(messages) {
			return messages.map(msg => {
				// 处理消息类型
				let type = msg.type || 'unknown'
				let payload = msg.payload || {}

				// 根据消息类型设置对应的type值
				switch(msg.type) {
					case 'TIMTextElem':
						type = 'TIMTextElem'
						break
					case 'TIMGroupTipElem':
						type = 'TIMGroupTipElem'
						break
					case 'TIMGroupSystemNoticeElem':
						type = 'TIMGroupSystemNoticeElem'
						break
					case 'TIMImageElem':
						type = 'TIMImageElem'
						break
					case 'TIMAudioElem':
						type = 'TIMAudioElem'
						break
					case 'TIMVideoElem':
						type = 'TIMVideoElem'
						break
					case 'TIMFileElem':
						type = 'TIMFileElem'
						break
					case 'TIMCustomElem':
						type = 'TIMCustomElem'
						break
					case 'TIMLocationElem':
						type = 'TIMLocationElem'
						break
					case 'TIMFaceElem':
						type = 'TIMFaceElem'
						break
					default:
						type = msg.type || 'unknown'
				}

				return {
					ID: msg.ID,
					from: msg.from,
					to: msg.to,
					nick: msg.nick,
					time: msg.time * 1000, // 转换为毫秒
					type: type,
					payload: payload, 
					avatar: msg.avatar || this.getParticipantAvatar(msg.from) || '/static/default-avatar.png',
					status: 'success'
				}
			}).sort((a, b) => a.time - b.time) // 按时间排序
		},
		
		// 重试发送消息
		async retryMessage(message) {
			// 更新消息状态为发送中
			const index = this.messageList.findIndex(msg => msg.ID === message.ID)
			if (index > -1) {
				this.messageList[index].status = 'sending'

				try {
					// 重新发送消息
					const result = await imManager.sendTextMessage(this.groupId, message.payload.text)

					if (result.success) {
						// 更新消息状态和ID
						this.messageList[index] = {
							...this.messageList[index],
							ID: result.data.message.ID,
							status: 'success'
						}
					} else {
						this.messageList[index].status = 'fail'
						uni.showToast({
							title: result.message || '重试发送失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('重试发送消息异常:', error)
					this.messageList[index].status = 'fail'
					uni.showToast({
						title: '重试发送失败',
						icon: 'none'
					})
				}
			}
		},

		// 发送消息
		async sendMessage() {
			const message = this.inputMessage.trim()
			if (!message) return
			
			// 创建临时消息对象用于显示
			const tempMessage = {
				ID: 'temp_' + Date.now(),
				nick: this.getUserInfo.nickname,
				from: this.currentUserID,
				to: this.groupId,
				time: Date.now(),
				type: 'TIMTextElem',
				payload: { text: message },
				avatar: this.getUserInfo.avatar || this.getParticipantAvatar(this.currentUserID) || '/static/default-avatar.png',
				status: 'sending'
			}
			
			// 添加到消息列表
			this.messageList.push(tempMessage)
			this.inputMessage = ''

			// 滚动到底部 - 发送消息后立即滚动
			this.$nextTick(() => {
				setTimeout(() => {
					console.log('发送消息后滚动到底部')
					this.scrollToBottom()
				}, 100)
			})
			
			try {
				console.log('开始发送消息:', message)
				// 发送消息
				const result = await imManager.sendTextMessage(this.groupId, message)
				console.log('发送消息结果:', result)

				// 更新消息状态
				const index = this.messageList.findIndex(msg => msg.ID === tempMessage.ID)
				console.log('查找临时消息索引:', index, '临时消息ID:', tempMessage.ID)

				if (index > -1) {
					if (result.success) {
						console.log('发送成功，更新消息状态')
						console.log('result.data:', result.data)

						// 获取真实的消息ID
						let realMessageID = tempMessage.ID // 默认使用临时ID
						if (result.data && result.data.message) {
							realMessageID = result.data.message.ID || result.data.message.id || tempMessage.ID
						}
						console.log('真实消息ID:', realMessageID)

						// 用真实消息替换临时消息
						this.messageList[index] = {
							...tempMessage,
							ID: realMessageID,
							status: 'success'
						}
						console.log('消息状态已更新为success')
					} else {
						console.log('发送失败，更新消息状态为fail')
						this.messageList[index].status = 'fail'
						uni.showToast({
							title: result.message || '发送失败',
							icon: 'none'
						})
					}
				}
				console.log('当前消息列表:', this.messageList);
				
			} catch (error) {
				console.error('发送消息异常:', error)
				// 更新消息状态为失败
				const index = this.messageList.findIndex(msg => msg.ID === tempMessage.ID)
				if (index > -1) {
					this.messageList[index].status = 'fail'
				}
				uni.showToast({
					title: '发送失败',
					icon: 'none'
				})
			}
		},
		
		// 接收新消息回调
		onNewMessage(event) {
			const messages = event || []
			console.log("我收到的新信息",messages);

			messages.forEach(msg => {
				// 只处理当前群组的消息
				if (msg.conversationType === 'GROUP' && msg.to === this.groupId) {
					const processedMsg = this.processMessages([msg])[0]
					if (processedMsg) {
						// 检查消息是否已存在
						const exists = this.messageList.some(existMsg => existMsg.ID === processedMsg.ID)
						if (!exists) {
							this.messageList.push(processedMsg)
							// 滚动到底部
							this.$nextTick(() => {
								this.scrollToBottom()
							})

							// 收到新消息后标记为已读
							this.markConversationAsRead()
						}
					}
				}
			})
		},

		// 标记会话为已读
		async markConversationAsRead() {
			try {
				// 检查IM状态
				const loginStatus = imManager.getLoginStatus()
				if (!loginStatus.isLoggedIn || !imManager.isSDKReady) {
					console.log('IM未登录或SDK未ready，跳过标记已读')
					return
				}

				const conversationID = `GROUP${this.groupId}`
				const result = await imManager.markConversationAsRead(conversationID)
				if (result.success) {
					console.log('标记会话已读成功')
				} else {
					console.error('标记会话已读失败:', result.error)
				}
			} catch (error) {
				console.error('标记会话已读异常:', error)
			}
		},
		
		// 滚动到底部
		scrollToBottom() {
			// 防抖处理，避免频繁滚动
			if (this.isScrolling) {
				return
			}

			this.isScrolling = true
			console.log('执行滚动到底部')

			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer)
			}

			this.scrollTimer = setTimeout(() => {
				this.$nextTick(() => {
					// 方法1: 使用 scrollIntoView (主要方案)
					this.scrollIntoView = 'bottom-anchor'

					// 方法2: 计算准确的滚动位置 (备用方案)
					setTimeout(() => {
						const query = uni.createSelectorQuery().in(this)
						query.select('.message-list').scrollOffset((res) => {
							if (res) {
								// 获取滚动区域的总高度
								const scrollHeight = res.scrollHeight
								const clientHeight = res.height

								// 计算需要滚动的距离
								const maxScrollTop = Math.max(0, scrollHeight - clientHeight)

								console.log('滚动信息:', {
									scrollHeight,
									clientHeight,
									maxScrollTop,
									currentScrollTop: res.scrollTop
								})

								// 设置滚动位置到底部
								this.scrollTop = maxScrollTop + 50 // 确保到底

								// 清除 scrollIntoView
								setTimeout(() => {
									this.scrollIntoView = ''
									this.isScrolling = false // 重置滚动状态
								}, 100)
							} else {
								this.isScrolling = false
							}
						}).exec()
					}, 150)
				})
			}, 50) // 50ms 防抖延迟
		},
		
		// 判断是否显示时间
		shouldShowTime(message, index) {
			if (index === 0) return true
			
			const prevMessage = this.messageList[index - 1]
			const timeDiff = message.time - prevMessage.time
			
			// 超过5分钟显示时间
			return timeDiff > 5 * 60 * 1000
		},
		
		// 格式化消息时间
		formatMessageTime(timestamp) {
			const date = new Date(timestamp)
			const now = new Date()
			const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
			const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
			
			const hours = date.getHours().toString().padStart(2, '0')
			const minutes = date.getMinutes().toString().padStart(2, '0')
			
			if (messageDate.getTime() === today.getTime()) {
				return `${hours}:${minutes}`
			} else {
				const month = (date.getMonth() + 1).toString().padStart(2, '0')
				const day = date.getDate().toString().padStart(2, '0')
				return `${month}-${day} ${hours}:${minutes}`
			}
		},
		
		// 输入框获得焦点
		onInputFocus() {
			this.inputFocus = true
			// 禁用自动滚动，防止键盘弹出时滚动条乱跳
			// setTimeout(() => {
			// 	this.scrollToBottom()
			// }, 300)
		},

		// 输入框失去焦点
		onInputBlur() {
			this.inputFocus = false
			// 键盘收起后滚动到底部
			setTimeout(() => {
				this.scrollToBottom()
			}, 100)
		},

		// 网络状态变化处理
		onNetworkStatusChange(res) {
			console.log('网络状态变化:', res)
			this.isConnected = res.isConnected

			if (!res.isConnected) {
				this.showNetworkTip = true
				this.isReconnecting = false
			} else {
				// 网络恢复，尝试重连IM
				this.attemptReconnect()
			}
		},

		// IM网络状态变化处理
		onIMNetStateChange(event) {
			console.log('IM网络状态变化:', event.data.state)

			switch(event.data.state) {
				case 'TIM.TYPES.NET_STATE_CONNECTED':
					this.showNetworkTip = false
					this.isReconnecting = false
					break
				case 'TIM.TYPES.NET_STATE_CONNECTING':
					this.showNetworkTip = true
					this.isReconnecting = true
					break
				case 'TIM.TYPES.NET_STATE_DISCONNECTED':
					this.showNetworkTip = true
					this.isReconnecting = false
					break
			}
		},

		// 尝试重连
		async attemptReconnect() {
			if (this.isReconnecting) return

			this.isReconnecting = true
			this.showNetworkTip = true

			try {
				// 检查IM登录状态，如果未登录则尝试重新登录
				const loginStatus = imManager.getLoginStatus()
				if (!loginStatus.isLoggedIn) {
					// 这里可以尝试重新获取IM配置并登录
					console.log('IM未登录，需要重新登录')
				}

				// 等待一段时间让IM自动重连
				setTimeout(() => {
					if (this.isConnected) {
						this.showNetworkTip = false
						this.isReconnecting = false
					}
				}, 3000)
			} catch (error) {
				console.error('重连失败:', error)
				this.isReconnecting = false
			}
		},

		// 判断是否为系统消息
		isSystemMessage(message) {
			return message.type === 'TIMGroupTipElem' || message.type === 'TIMGroupSystemNoticeElem'
		},

		// 格式化群提示消息
		formatGroupTipMessage(message) {
			const payload = message.payload
			if (!payload) return '群提示消息'
			
			const operatorNick = payload.operatorID ? (payload.operatorID === this.currentUserID ? '你' : payload.operatorID) : '系统'
			const userList = payload.memberList || []

			switch(payload.operationType) {
				case 1: // 有成员加群
					if (userList.length === 1) {
						console.log(userNick);
						
						const userNick = userList[0].userID === this.currentUserID ? '你' : userList[0]
						return `${userNick.nick?`${userNick.nick}`:'你'} 加入了群聊`
					} else {
						return `${userList.length}名成员加入了群聊`
					}
				case 2: // 有群成员退群
					if (userList.length === 1) {
						const userNick = userList[0].userID === this.currentUserID ? '你' : userList[0]
						return `${userNick.nick} 退出了群聊`
					} else {
						return `${userList.length}名成员退出了群聊`
					}
				case 3: // 有群成员被踢出群
					if (userList.length === 1) {
						const userNick = userList[0].userID === this.currentUserID ? '你' : userList[0]
						return `${userNick.nick} 被 管理员 移出群聊`
					} else {
						return `${userList.length}名成员被 管理员 移出群聊`
					}
				case 4: // 有群成员被设为管理员
					const adminNick = userList[0].userID === this.currentUserID ? '你' : userList[0]
					return `${adminNick} 被 ${operatorNick} 设为管理员`
				case 5: // 有群成员被撤销管理员
					const memberNick = userList[0].userID === this.currentUserID ? '你' : userList[0]
					return `${memberNick} 被 ${operatorNick} 撤销管理员`
				case 6: // 群组资料变更
					return `${operatorNick} 修改了群资料`
				case 7: // 群成员资料变更
					return `群成员资料发生变更`
				default:
					return '群提示消息'
			}
		},

		// 格式化群系统通知
		formatGroupSystemNotice(message) {
			const payload = message.payload
			if (!payload) return '群系统通知'

			switch(payload.operationType) {
				case 1:
					return '有用户申请加群'
				case 2:
					return '申请加群被同意'
				case 3:
					return '申请加群被拒绝'
				case 4:
					return '你被踢出群组'
				case 5:
					return '群组已解散'
				case 6:
					return '群组创建成功'
				case 7:
					return '你被邀请加入群组'
				case 8:
					return '你已退出群组'
				case 9:
					return '你被设为管理员'
				case 10:
					return '你被取消管理员'
				case 11:
					return '群已被回收'
				case 12:
					return '收到加群邀请'
				default:
					return '群系统通知'
			}
		},

		// 获取图片URL
		getImageUrl(payload) {
			if (!payload || !payload.imageInfoArray) return ''
			// 优先使用720p压缩图，其次是198p压缩图，最后是原图
			const imageInfo = payload.imageInfoArray.find(info => info.type === 2) ||
							  payload.imageInfoArray.find(info => info.type === 1) ||
							  payload.imageInfoArray.find(info => info.type === 0)
			return imageInfo ? imageInfo.url : ''
		},

		// 预览图片
		previewImage(payload) {
			if (!payload || !payload.imageInfoArray) return
			const urls = payload.imageInfoArray.map(info => info.url)
			uni.previewImage({
				urls: urls,
				current: this.getImageUrl(payload)
			})
		},

		// 播放音频
		playAudio(payload) {
			if (!payload || !payload.url) return
			// 这里可以实现音频播放逻辑
			uni.showToast({
				title: '音频播放功能待实现',
				icon: 'none'
			})
		},

		// 播放视频
		playVideo(payload) {
			if (!payload || !payload.videoUrl) return
			// 这里可以实现视频播放逻辑
			uni.showToast({
				title: '视频播放功能待实现',
				icon: 'none'
			})
		},

		// 下载文件
		downloadFile(payload) {
			if (!payload || !payload.fileUrl) return
			// 这里可以实现文件下载逻辑
			uni.showToast({
				title: '文件下载功能待实现',
				icon: 'none'
			})
		},

		// 格式化文件大小
		formatFileSize(size) {
			if (!size) return '0B'
			const units = ['B', 'KB', 'MB', 'GB']
			let index = 0
			while (size >= 1024 && index < units.length - 1) {
				size /= 1024
				index++
			}
			return `${size.toFixed(1)}${units[index]}`
		}
	}
}
</script>

<style lang="scss" scoped>
/* 全局页面样式重置 */
page {
	height: 100%;
	overflow: hidden;
}

/* 确保页面容器不会产生滚动条 */
.uni-page-body {
	height: 100% !important;
	overflow: hidden !important;
}
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
	overflow: hidden; /* 防止整体页面滚动 */
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		position: absolute;
		left: 30rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.back-icon {
			width: 36rpx;
			height: 36rpx;
			position: relative;

			.back-arrow {
				width: 20rpx;
				height: 20rpx;
				border-left: 4rpx solid #fff;
				border-bottom: 4rpx solid #fff;
				transform: rotate(45deg);
				position: absolute;
				left: 8rpx;
				top: 8rpx;
			}
		}
	}

	.title {
		flex: 1;
		text-align: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.network-tip {
	position: fixed;
	top: calc(var(--status-bar-height) + 88rpx);
	left: 0;
	right: 0;
	background-color: #ff9800;
	color: #fff;
	text-align: center;
	padding: 16rpx;
	z-index: 998;
	font-size: 24rpx;
}

.network-text {
	color: #fff;
}

.chat-content {
	flex: 1;
	margin-top: calc(var(--status-bar-height) + 88rpx);
	margin-bottom: 120rpx;
	margin-bottom: calc(120rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
	margin-bottom: calc(120rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
	position: relative;
	overflow: hidden; /* 防止内容区域溢出 */
}

.message-list {
	height: 100%;
	width: 100%;
	padding: 20rpx;
	box-sizing: border-box;
	/* 隐藏滚动条但保持滚动功能 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE 10+ */
	/* 优化滚动性能 */
	-webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
	scroll-behavior: smooth; /* 平滑滚动 */
	/* 防止滚动回弹 */
	overscroll-behavior: contain;
}

/* 隐藏 WebKit 浏览器的滚动条 */
.message-list::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
}

.load-more {
	text-align: center;
	padding: 20rpx;
}

.load-more-text {
	font-size: 24rpx;
	color: #999;
}

.message-item {
	margin-bottom: 30rpx;
	flex-wrap: wrap;
}

/* 系统消息布局 */
.message-system {
	display: flex;
	justify-content: center;
	align-items: center;
}

.system-message-wrapper {
	display: flex;
	justify-content: center;
	width: 100%;
}

.time-divider {
	text-align: center;
	margin: 20rpx 0;
	width: 100%
}

.time-text {
	font-size: 24rpx;
	color: #999;
	background-color: rgba(255, 255, 255, 0.8);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.message-wrapper {
	display: flex;
	align-items: flex-start;
}

.message-self .message-wrapper {
	flex-direction: row-reverse;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin: 0 20rpx;
}

.message-bubble {
	max-width: 60%;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 20rpx;
	position: relative;
}

.message-self .message-bubble {
	background-color: #4CAF50;
}

/* 系统消息样式 */
.system-message {
	background-color: #f0f0f0 !important;
	max-width: 80% !important;
	text-align: center;
	margin: 0 auto;
}

.system-text {
	color: #666 !important;
	font-size: 24rpx !important;
}

.nickname {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.message-self .nickname {
	display: none;
}

.message-content {
	word-wrap: break-word;
}

.message-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.4;
}

.message-self .message-text {
	color: #fff;
}

.message-status {
	margin-top: 8rpx;
	text-align: right;
}

.status-text {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.7);
}

.status-text.error {
	color: #ff6b6b;
}

.status-fail {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.retry-btn {
	font-size: 22rpx;
	color: #4CAF50;
	padding: 8rpx 16rpx;
	border: 1rpx solid #4CAF50;
	border-radius: 12rpx;
	background-color: rgba(76, 175, 80, 0.1);
}

.bottom-anchor {
	height: 1rpx;
}

.input-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-top: 1rpx solid #e0e0e0;
	padding: 20rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
	z-index: 998;
	box-sizing: border-box;
}

.input-wrapper {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.message-input {
	flex: 1;
	height: 80rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333;
}

.send-btn {
	background-color: #4CAF50;
	color: #fff;
	font-size: 28rpx;
	padding: 20rpx 32rpx;
	border-radius: 40rpx;
	border: none;
	min-width: 120rpx;
}

.send-btn[disabled] {
	background-color: #ccc;
	color: #999;
}

/* 图片消息样式 */
.message-image {
	max-width: 400rpx;
	max-height: 400rpx;
	border-radius: 12rpx;
}

/* 音频消息样式 */
.audio-message {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx;
	background-color: rgba(0, 0, 0, 0.05);
	border-radius: 12rpx;
	min-width: 200rpx;
}

.audio-icon {
	font-size: 32rpx;
}

/* 视频消息样式 */
.video-message {
	position: relative;
	display: inline-block;
}

.video-thumb {
	max-width: 400rpx;
	max-height: 300rpx;
	border-radius: 12rpx;
}

.video-play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-size: 48rpx;
	color: #fff;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 文件消息样式 */
.file-message {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx;
	background-color: rgba(0, 0, 0, 0.05);
	border-radius: 12rpx;
	min-width: 300rpx;
}

.file-icon {
	font-size: 32rpx;
}

.file-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.file-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.file-size {
	font-size: 24rpx;
	color: #999;
}

/* 位置消息样式 */
.location-message {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx;
	background-color: rgba(0, 0, 0, 0.05);
	border-radius: 12rpx;
	min-width: 200rpx;
}

.location-icon {
	font-size: 32rpx;
}

/* 表情消息样式 */
.face-message {
	font-size: 48rpx;
	line-height: 1;
}
</style>
