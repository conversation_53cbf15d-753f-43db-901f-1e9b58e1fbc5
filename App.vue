<script>
	import { getIMConfig } from './Api/index.js'
	import imManager from './utils/IMManager.js'

	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		async mounted() {
			await this.$verify()
			// 如果用户已登录，初始化IM
			if (uni.getStorageSync('ydToken') && this.$store.state.userinfo.id) {
				this.initIMIfLoggedIn()
			}
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 如果已登录则初始化IM
			async initIMIfLoggedIn() {
				try {
					// 检查是否已经登录IM
					const loginStatus = imManager.getLoginStatus()
					if (loginStatus.isLoggedIn) {
						console.log('IM已经登录，跳过初始化')
						return
					}

					const imConfigRes = await getIMConfig()
					if (imConfigRes.data.code === 200) {
						const imConfig = imConfigRes.data.data

						// 初始化IM SDK
						const initSuccess = imManager.init({
							SDKAppID: imConfig.SDKAppID
						})

						if (initSuccess) {
							// 登录IM
							const loginResult = await imManager.login(imConfig.userID, imConfig.userSig)
							if (loginResult.success) {
								console.log('应用启动时IM登录成功')
								// 登录成功回调会在IMManager的login方法中自动触发
							} else {
								console.error('应用启动时IM登录失败:', loginResult.error)
							}
						}
					}
				} catch (error) {
					console.error('应用启动时IM初始化失败:', error)
				}
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	view,scroll-view{
		box-sizing: border-box!important;
	}
	.location-card {
		background: #fff;
		border-radius: 24rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
		
		.location-image {
			width: 100%;
			height: 350rpx;
		}
		
		.location-info {
			padding: 15rpx;
			
			.location-name {
				font-size: 32rpx;
				font-weight: 500;
				margin-bottom: 16rpx;
			}
			
			.location-desc {
				font-size: 28rpx;
				color: #666;
				margin:20rpx 0;
			}
			
			.location-meta {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.distance {
					font-size: 28rpx;
					color: #888;
				}
				
				.navigate-btn {
					margin: 0;
					background: #4CAF50;
					color: #fff;
					font-size: 28rpx;
					display: flex;
					align-items: center;
					
					.iconfont {
						margin-right: 8rpx;
					}
				}
			}
		}
	}
	/* 状态栏适配 */
	.status-bar {
		height: var(--status-bar-height);
		width: 100%;
	}
	
	/* 导航栏样式 */
	.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background-color: #ffffff;
	}
	
	/* 页面内容区域 */
	.page-content {
		padding-top: calc(var(--status-bar-height) + 44px);
	}
	
	/* 安全区域适配 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
